package cn.iflytek.domain.agent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * RAG知识库实体 - 简化版本
 * 只存储基础元数据，文档内容和向量数据由Spring AI自动管理
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiRagKnowledgeBase {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 知识库名称（唯一）
     */
    private String name;
    
    /**
     * 知识库描述
     */
    private String description;
    
    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;
    
    /**
     * 文档数量
     */
    private Integer documentCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 检查知识库是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }
    
    /**
     * 启用知识库
     */
    public void enable() {
        this.status = 1;
    }
    
    /**
     * 禁用知识库
     */
    public void disable() {
        this.status = 0;
    }
}
