-- AI Agent 简化RAG业务表结构 (MariaDB)
-- 创建时间: 2025-08-03
-- 数据库: ai_agent_db
-- 说明: 只保留必要的知识库元数据表，向量数据由Spring AI自动管理

USE ai_agent_db;

-- 简化的RAG知识库表 - 只存储基础元数据
CREATE TABLE `ai_rag_knowledge_base` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(100) NOT NULL COMMENT '知识库名称',
    `description` TEXT COMMENT '知识库描述',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
    `document_count` INT NOT NULL DEFAULT 0 COMMENT '文档数量',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`name`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RAG知识库元数据表';

-- 插入示例数据
INSERT INTO `ai_rag_knowledge_base` (`name`, `description`) VALUES 
('default', '默认知识库'),
('test-kb', '测试知识库');

-- 注意事项:
-- 1. 文档内容和向量数据完全由Spring AI的vector_store表管理
-- 2. 这张表只存储知识库的基础元数据信息
-- 3. 文档的详细信息通过vector_store表的metadata字段存储
-- 4. 知识库与文档的关联通过metadata中的rag_name字段实现
