# RAG 简化架构设计

## 设计理念

基于您的正确观点：**Spring AI 会自动创建向量库进行检索，不需要复杂的业务表来存储文档内容**。

## 简化后的架构

### 1. 数据存储层

```
┌─────────────────────────────────────────────────────────────┐
│                    MariaDB 数据库                            │
├─────────────────────────────────────────────────────────────┤
│  业务表（我们管理）                                          │
│  ├── ai_rag_knowledge_base  (知识库元数据)                  │
│  │   ├── id, name, description                             │
│  │   ├── status, document_count                            │
│  │   └── create_time, update_time                          │
│                                                             │
│  Spring AI 自动管理的表                                     │
│  ├── vector_store  (向量存储表)                             │
│  │   ├── id (文档ID)                                       │
│  │   ├── content (文档内容)                                │
│  │   ├── metadata (元数据JSON)                             │
│  │   └── embedding (向量数据)                              │
└─────────────────────────────────────────────────────────────┘
```

### 2. 数据流程

```
文件上传 → 文档处理 → Spring AI 自动向量化 → 存储到 vector_store
    ↓
更新知识库元数据表的文档计数
```

### 3. 核心优势

1. **简单明了**：只有一张业务表存储知识库基础信息
2. **Spring AI 托管**：文档内容、分块、向量化完全由 Spring AI 自动处理
3. **元数据关联**：通过 `metadata.rag_name` 字段关联知识库
4. **无数据冗余**：文档内容只存储在 vector_store 中
5. **易于维护**：减少了复杂的表关系和数据同步问题

## 实现方式

### 文档上传流程

```java
// 1. 创建 Spring AI Document
Document document = new Document(content, metadata);
metadata.put("rag_name", knowledgeBaseName);
metadata.put("title", title);
metadata.put("source", source);

// 2. Spring AI 自动处理向量化和存储
vectorStore.add(List.of(document));

// 3. 更新业务表的文档计数
knowledgeBaseService.incrementDocumentCount(knowledgeBaseName);
```

### 文档检索流程

```java
// 直接使用 Spring AI 的向量搜索
List<Document> results = vectorStore.similaritySearch(
    SearchRequest.builder()
        .query(query)
        .topK(topK)
        .build()
);

// 通过 metadata.rag_name 过滤特定知识库的文档
List<Document> filtered = results.stream()
    .filter(doc -> knowledgeBaseName.equals(doc.getMetadata().get("rag_name")))
    .collect(Collectors.toList());
```

## 与原方案对比

| 方面 | 原复杂方案 | 简化方案 |
|------|------------|----------|
| 业务表数量 | 3张表 | 1张表 |
| 数据冗余 | 高（文档内容重复存储） | 无 |
| 维护复杂度 | 高（需要同步多张表） | 低 |
| Spring AI 集成 | 复杂 | 原生支持 |
| 扩展性 | 受限于表结构 | 灵活（基于metadata） |

## 总结

这个简化方案完全符合您的理念：
- **一张表足够**：只需要 `ai_rag_knowledge_base` 存储基础元数据
- **Spring AI 托管**：文档内容和向量数据完全由 Spring AI 自动管理
- **简单高效**：减少了不必要的复杂性，提高了开发和维护效率

这就是为什么 Spring AI 这么强大的原因 - 它已经为我们处理了所有复杂的向量存储逻辑！
