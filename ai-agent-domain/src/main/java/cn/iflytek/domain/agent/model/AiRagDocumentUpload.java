package cn.iflytek.domain.agent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * RAG文档上传请求 - 简化版本
 * 只包含必要的文档信息，由Spring AI自动处理向量化和存储
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiRagDocumentUpload {
    
    /**
     * 知识库名称
     */
    private String knowledgeBaseName;
    
    /**
     * 文档标题
     */
    private String title;
    
    /**
     * 文档内容
     */
    private String content;
    
    /**
     * 文档来源（可选）
     */
    private String source;
    
    /**
     * 自定义元数据（可选）
     * 这些元数据会被添加到Spring AI的Document中
     */
    private Map<String, Object> metadata;
    
    /**
     * 分块大小（可选，默认1000）
     */
    private Integer chunkSize;
    
    /**
     * 分块重叠（可选，默认200）
     */
    private Integer chunkOverlap;
    
    /**
     * 获取默认分块大小
     */
    public int getChunkSizeOrDefault() {
        return chunkSize != null ? chunkSize : 1000;
    }
    
    /**
     * 获取默认分块重叠
     */
    public int getChunkOverlapOrDefault() {
        return chunkOverlap != null ? chunkOverlap : 200;
    }
}
