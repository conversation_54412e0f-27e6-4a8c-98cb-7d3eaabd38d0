-- AI Agent 示例数据 (MariaDB)
-- 创建时间: 2025-08-03
-- 数据库: ai_agent_db

USE ai_agent_db;

-- 插入系统提示词示例数据
INSERT INTO ai_client_system_prompt (prompt_content) VALUES 
('你是一个专业的AI助手，请根据提供的上下文信息回答用户的问题。'),
('你是一个技术专家，专门回答关于Spring AI和RAG技术的问题。'),
('你是一个友好的客服助手，请耐心回答用户的问题。');

-- 插入模型配置示例数据
INSERT INTO ai_client_model (model_name, base_url, api_key, model_type, model_version) VALUES 
('gpt-3.5-turbo', 'https://api.openai.com', 'your-openai-api-key', 'openai', '3.5'),
('text-embedding-3-small', 'https://api.openai.com', 'your-openai-api-key', 'openai', '3.0'),
('gpt-4', 'https://api.openai.com', 'your-openai-api-key', 'openai', '4.0');

-- 插入顾问配置示例数据
INSERT INTO ai_client_advisor (advisor_name, advisor_type, order_num, chat_memory_max_messages, rag_answer_top_k, rag_answer_filter_expression) VALUES 
('记忆管理顾问', 'PromptChatMemory', 1, 10, NULL, NULL),
('RAG问答顾问', 'RagAnswer', 2, NULL, 5, 'type == "document"'),
('日志记录顾问', 'SimpleLoggerAdvisor', 3, NULL, NULL, NULL);

-- 插入MCP工具配置示例数据
INSERT INTO ai_client_tool_mcp (mcp_name, transport_type, sse_base_uri, sse_endpoint, request_timeout) VALUES 
('weather-tool', 'sse', 'http://localhost:8080', '/sse', 5),
('search-tool', 'sse', 'http://localhost:8081', '/search', 10);

-- 插入AI客户端示例数据
INSERT INTO ai_client (system_prompt_id, model_bean_id) VALUES 
(1, 1),
(2, 3),
(3, 1);

-- 插入客户端与顾问关联示例数据
INSERT INTO ai_client_advisor_rel (client_id, advisor_bean_id) VALUES 
(1, 1),
(1, 2),
(2, 2),
(2, 3),
(3, 1);

-- 插入RAG知识库示例数据 (简化版本)
INSERT INTO ai_rag_knowledge_base (name, description, status, document_count) VALUES
('技术文档库', 'Spring AI和RAG相关技术文档', 1, 0),
('产品手册库', '产品使用手册和FAQ', 1, 0),
('测试知识库', '用于测试的示例知识库', 1, 0),
('default', '默认知识库', 1, 0);

-- 注意：不再插入RAG文档和分块数据
-- 原因：文档内容和向量数据完全由Spring AI的vector_store表管理
-- 文档上传将通过API接口完成，Spring AI会自动处理向量化和存储

-- 显示插入结果
SELECT '数据插入完成 - 简化RAG架构' as status;
SELECT COUNT(*) as system_prompt_count FROM ai_client_system_prompt;
SELECT COUNT(*) as model_count FROM ai_client_model;
SELECT COUNT(*) as advisor_count FROM ai_client_advisor;
SELECT COUNT(*) as client_count FROM ai_client;
SELECT COUNT(*) as knowledge_base_count FROM ai_rag_knowledge_base;
SELECT '文档数据由Spring AI自动管理' as document_info;
