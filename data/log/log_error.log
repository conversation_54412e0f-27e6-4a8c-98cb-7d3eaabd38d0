25-08-03.17:51:12.165 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.17:51:12.165 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:51:12.167 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.17:51:14.227 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'aiClientInitializer': Injection of resource dependencies failed
25-08-03.17:51:14.278 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator.validateAndEnquoteIdentifier(MariaDBSchemaValidator.java:154)

The following method did not exist:

    'java.lang.String org.mariadb.jdbc.Driver.enquoteIdentifier(java.lang.String, boolean)'

The calling method's class, org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator, was loaded from the following location:

    jar:file:/C:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-mariadb-store/1.0.1/spring-ai-mariadb-store-1.0.1.jar!/org/springframework/ai/vectorstore/mariadb/MariaDBSchemaValidator.class

The called method's class, org.mariadb.jdbc.Driver, is available from the following locations:

    jar:file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar!/org/mariadb/jdbc/Driver.class

The called method's class hierarchy was loaded from the following locations:

    org.mariadb.jdbc.Driver: file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator and org.mariadb.jdbc.Driver

25-08-03.17:56:15.448 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'vectorStore' defined in class path resource [org/springframework/ai/vectorstore/mariadb/autoconfigure/MariaDbStoreAutoConfiguration.class]: Cannot register bean definition [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=org.springframework.ai.vectorstore.mariadb.autoconfigure.MariaDbStoreAutoConfiguration; factoryMethodName=vectorStore; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [org/springframework/ai/vectorstore/mariadb/autoconfigure/MariaDbStoreAutoConfiguration.class]] for bean 'vectorStore' since there is already [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=vectorStoreConfig; factoryMethodName=vectorStore; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [cn/iflytek/config/VectorStoreConfig.class]] bound.
25-08-03.17:56:15.541 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'vectorStore', defined in class path resource [org/springframework/ai/vectorstore/mariadb/autoconfigure/MariaDbStoreAutoConfiguration.class], could not be registered. A bean with that name has already been defined in class path resource [cn/iflytek/config/VectorStoreConfig.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

25-08-03.17:57:49.388 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.388 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.388 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.17:57:51.257 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'aiClientInitializer': Injection of resource dependencies failed
25-08-03.17:57:51.317 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator.validateAndEnquoteIdentifier(MariaDBSchemaValidator.java:155)

The following method did not exist:

    'java.lang.String org.mariadb.jdbc.Driver.enquoteIdentifier(java.lang.String, boolean)'

The calling method's class, org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator, was loaded from the following location:

    jar:file:/C:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-mariadb-store/1.0.0-M8/spring-ai-mariadb-store-1.0.0-M8.jar!/org/springframework/ai/vectorstore/mariadb/MariaDBSchemaValidator.class

The called method's class, org.mariadb.jdbc.Driver, is available from the following locations:

    jar:file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar!/org/mariadb/jdbc/Driver.class

The called method's class hierarchy was loaded from the following locations:

    org.mariadb.jdbc.Driver: file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator and org.mariadb.jdbc.Driver

25-08-03.17:59:10.294 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.294 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.294 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.294 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.17:59:12.201 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'aiClientInitializer': Injection of resource dependencies failed
25-08-03.17:59:12.245 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator.validateAndEnquoteIdentifier(MariaDBSchemaValidator.java:155)

The following method did not exist:

    'java.lang.String org.mariadb.jdbc.Driver.enquoteIdentifier(java.lang.String, boolean)'

The calling method's class, org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator, was loaded from the following location:

    jar:file:/C:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-mariadb-store/1.0.0-M8/spring-ai-mariadb-store-1.0.0-M8.jar!/org/springframework/ai/vectorstore/mariadb/MariaDBSchemaValidator.class

The called method's class, org.mariadb.jdbc.Driver, is available from the following locations:

    jar:file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar!/org/mariadb/jdbc/Driver.class

The called method's class hierarchy was loaded from the following locations:

    org.mariadb.jdbc.Driver: file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator and org.mariadb.jdbc.Driver

