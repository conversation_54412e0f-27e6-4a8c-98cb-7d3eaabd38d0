25-08-03.17:51:10.508 [main            ] INFO  Application            - Starting Application using Java 17.0.9 with PID 5108 (C:\Users\<USER>\Downloads\ai-agent\ai-agent-app\target\classes started by 17813 in C:\Users\<USER>\Downloads\ai-agent)
25-08-03.17:51:10.512 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-03.17:51:12.165 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.17:51:12.165 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ai<PERSON><PERSON><PERSON>apper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:51:12.166 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:51:12.167 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.17:51:12.860 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-03.17:51:12.888 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-03.17:51:12.890 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-03.17:51:12.891 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-03.17:51:13.028 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-03.17:51:13.028 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2442 ms
25-08-03.17:51:14.227 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'aiClientInitializer': Injection of resource dependencies failed
25-08-03.17:51:14.231 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-08-03.17:51:14.256 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-03.17:51:14.278 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator.validateAndEnquoteIdentifier(MariaDBSchemaValidator.java:154)

The following method did not exist:

    'java.lang.String org.mariadb.jdbc.Driver.enquoteIdentifier(java.lang.String, boolean)'

The calling method's class, org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator, was loaded from the following location:

    jar:file:/C:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-mariadb-store/1.0.1/spring-ai-mariadb-store-1.0.1.jar!/org/springframework/ai/vectorstore/mariadb/MariaDBSchemaValidator.class

The called method's class, org.mariadb.jdbc.Driver, is available from the following locations:

    jar:file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar!/org/mariadb/jdbc/Driver.class

The called method's class hierarchy was loaded from the following locations:

    org.mariadb.jdbc.Driver: file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator and org.mariadb.jdbc.Driver

25-08-03.17:56:13.843 [main            ] INFO  Application            - Starting Application using Java 17.0.9 with PID 22512 (C:\Users\<USER>\Downloads\ai-agent\ai-agent-app\target\classes started by 17813 in C:\Users\<USER>\Downloads\ai-agent)
25-08-03.17:56:13.846 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-03.17:56:15.448 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'vectorStore' defined in class path resource [org/springframework/ai/vectorstore/mariadb/autoconfigure/MariaDbStoreAutoConfiguration.class]: Cannot register bean definition [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=org.springframework.ai.vectorstore.mariadb.autoconfigure.MariaDbStoreAutoConfiguration; factoryMethodName=vectorStore; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [org/springframework/ai/vectorstore/mariadb/autoconfigure/MariaDbStoreAutoConfiguration.class]] for bean 'vectorStore' since there is already [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=vectorStoreConfig; factoryMethodName=vectorStore; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [cn/iflytek/config/VectorStoreConfig.class]] bound.
25-08-03.17:56:15.460 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-03.17:56:15.541 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'vectorStore', defined in class path resource [org/springframework/ai/vectorstore/mariadb/autoconfigure/MariaDbStoreAutoConfiguration.class], could not be registered. A bean with that name has already been defined in class path resource [cn/iflytek/config/VectorStoreConfig.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

25-08-03.17:57:48.168 [main            ] INFO  Application            - Starting Application using Java 17.0.9 with PID 8348 (C:\Users\<USER>\Downloads\ai-agent\ai-agent-app\target\classes started by 17813 in C:\Users\<USER>\Downloads\ai-agent)
25-08-03.17:57:48.170 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-03.17:57:49.388 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.388 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.388 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:57:49.389 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.17:57:49.970 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-03.17:57:49.988 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-03.17:57:49.990 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-03.17:57:49.991 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-03.17:57:50.088 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-03.17:57:50.089 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1569 ms
25-08-03.17:57:51.257 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'aiClientInitializer': Injection of resource dependencies failed
25-08-03.17:57:51.261 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-08-03.17:57:51.284 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-03.17:57:51.317 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator.validateAndEnquoteIdentifier(MariaDBSchemaValidator.java:155)

The following method did not exist:

    'java.lang.String org.mariadb.jdbc.Driver.enquoteIdentifier(java.lang.String, boolean)'

The calling method's class, org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator, was loaded from the following location:

    jar:file:/C:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-mariadb-store/1.0.0-M8/spring-ai-mariadb-store-1.0.0-M8.jar!/org/springframework/ai/vectorstore/mariadb/MariaDBSchemaValidator.class

The called method's class, org.mariadb.jdbc.Driver, is available from the following locations:

    jar:file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar!/org/mariadb/jdbc/Driver.class

The called method's class hierarchy was loaded from the following locations:

    org.mariadb.jdbc.Driver: file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator and org.mariadb.jdbc.Driver

25-08-03.17:59:09.073 [main            ] INFO  Application            - Starting Application using Java 17.0.9 with PID 22644 (C:\Users\<USER>\Downloads\ai-agent\ai-agent-app\target\classes started by 17813 in C:\Users\<USER>\Downloads\ai-agent)
25-08-03.17:59:09.075 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-03.17:59:10.294 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.294 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.294 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.294 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.17:59:10.295 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.17:59:10.881 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-03.17:59:10.903 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-03.17:59:10.905 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-03.17:59:10.905 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-03.17:59:11.031 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-03.17:59:11.031 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1623 ms
25-08-03.17:59:12.201 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'aiClientInitializer': Injection of resource dependencies failed
25-08-03.17:59:12.206 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-08-03.17:59:12.224 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-08-03.17:59:12.245 [main            ] ERROR LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

An attempt was made to call a method that does not exist. The attempt was made from the following location:

    org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator.validateAndEnquoteIdentifier(MariaDBSchemaValidator.java:155)

The following method did not exist:

    'java.lang.String org.mariadb.jdbc.Driver.enquoteIdentifier(java.lang.String, boolean)'

The calling method's class, org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator, was loaded from the following location:

    jar:file:/C:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-mariadb-store/1.0.0-M8/spring-ai-mariadb-store-1.0.0-M8.jar!/org/springframework/ai/vectorstore/mariadb/MariaDBSchemaValidator.class

The called method's class, org.mariadb.jdbc.Driver, is available from the following locations:

    jar:file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar!/org/mariadb/jdbc/Driver.class

The called method's class hierarchy was loaded from the following locations:

    org.mariadb.jdbc.Driver: file:/C:/Users/<USER>/.m2/repository/org/mariadb/jdbc/mariadb-java-client/3.4.1/mariadb-java-client-3.4.1.jar


Action:

Correct the classpath of your application so that it contains compatible versions of the classes org.springframework.ai.vectorstore.mariadb.MariaDBSchemaValidator and org.mariadb.jdbc.Driver

